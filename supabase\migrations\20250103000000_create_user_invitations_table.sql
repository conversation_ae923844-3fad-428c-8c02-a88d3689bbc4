/*
  # Create user_invitations table

  1. New Tables
    - `user_invitations`
      - `id` (uuid, primary key)
      - `email` (text, not null)
      - `name` (text, not null)
      - `role` (text with check constraint)
      - `department` (text, optional)
      - `location` (text, optional)
      - `manager_id` (uuid, foreign key to users)
      - `temp_password` (text, not null)
      - `invite_token` (text, unique, not null)
      - `status` (text with check constraint, default 'pending')
      - `created_by` (uuid, foreign key to users)
      - `created_at` (timestamptz with default)
      - `expires_at` (timestamptz, not null)
      - `accepted_at` (timestamptz, optional)

  2. Security
    - Enable RLS on `user_invitations` table
    - Add policy for admins to manage all invitations
    - Add policy for users to view invitations by token

  3. Indexes
    - Add indexes for better performance on token, email, and status
*/

-- Create user_invitations table
CREATE TABLE IF NOT EXISTS user_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('employee', 'manager', 'admin')),
  department TEXT,
  location TEXT,
  manager_id UUID REFERENCES users(id),
  temp_password TEXT NOT NULL,
  invite_token TEXT UNIQUE NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired')),
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  accepted_at TIMESTAMPTZ
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_invitations_token ON user_invitations(invite_token);
CREATE INDEX IF NOT EXISTS idx_user_invitations_email ON user_invitations(email);
CREATE INDEX IF NOT EXISTS idx_user_invitations_status ON user_invitations(status);

-- Enable RLS
ALTER TABLE user_invitations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Admins can manage all invitations" ON user_invitations
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
);

CREATE POLICY "Users can view invitations by token" ON user_invitations
FOR SELECT USING (true); -- Anyone can read with token for signup

-- Grant permissions
GRANT ALL ON user_invitations TO authenticated;
GRANT SELECT ON user_invitations TO anon; -- Allow anonymous users to read invitations for signup
